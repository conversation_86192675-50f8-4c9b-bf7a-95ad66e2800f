import { useState, useEffect } from 'react'
import { CustomerToken, CreateCustomerTokenRequest, UpdateCustomerTokenRequest, OAuth2Provider } from '@rpa-project/shared'
import { customerApi } from '../../services/api'
import { Portal } from '../ui/Portal'
import { OAuth2Modal } from './OAuth2Modal'

interface CustomerTokenFormProps {
  customerId: string
  token?: CustomerToken | null
  onSuccess: () => void
  onCancel: () => void
}

export function CustomerTokenForm({ customerId, token, onSuccess, onCancel }: CustomerTokenFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    provider: 'manual' as OAuth2Provider,
    apiToken: '',
    refreshToken: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showOAuth2Modal, setShowOAuth2Modal] = useState(false)

  const isEditing = !!token

  useEffect(() => {
    if (token) {
      setFormData({
        name: token.name,
        description: token.description || '',
        provider: token.provider || 'manual',
        apiToken: '',
        refreshToken: ''
      })
    }
  }, [token])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      if (isEditing) {
        const updateRequest: UpdateCustomerTokenRequest = {
          name: formData.name || undefined,
          description: formData.description || undefined,
          provider: formData.provider,
          apiToken: formData.apiToken || undefined,
          refreshToken: formData.refreshToken || undefined
        }
        
        // Remove empty fields
        Object.keys(updateRequest).forEach(key => {
          if (updateRequest[key as keyof UpdateCustomerTokenRequest] === '') {
            delete updateRequest[key as keyof UpdateCustomerTokenRequest]
          }
        })

        const response = await customerApi.updateCustomerToken(customerId, token!.id, updateRequest)
        if (!response.success) {
          throw new Error(response.error || 'Failed to update token')
        }
      } else {
        const createRequest: CreateCustomerTokenRequest = {
          name: formData.name,
          description: formData.description || undefined,
          provider: formData.provider,
          apiToken: formData.apiToken || undefined,
          refreshToken: formData.refreshToken || undefined
        }

        const response = await customerApi.createCustomerToken(customerId, createRequest)
        if (!response.success) {
          throw new Error(response.error || 'Failed to create token')
        }
      }

      onSuccess()
    } catch (err: any) {
      setError(err.message || 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: string, value: string | OAuth2Provider) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleOAuth2Success = (provider: OAuth2Provider, tokenName: string) => {
    setShowOAuth2Modal(false)
    onSuccess() // Refresh the token list
  }

  return (
    <>
    <Portal>
      <div className="modal-overlay" onClick={onCancel}>
        <div
          className="modal-content"
          style={{ width: '100%', maxWidth: '70vw' }}
          onClick={(e) => e.stopPropagation()}
        >
        {/* Modal Header */}
        <div style={{
          flexShrink: 0,
          borderBottom: '1px solid #e5e7eb',
          margin: 0,
          padding: '1.5rem',
          textAlign: 'left'
        }}>
          <p className="dashboard-title" style={{ fontSize: '1.5rem', margin: 0 }}>
            {isEditing ? 'Redigera token' : 'Ny token'}
          </p>
        </div>

        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          <div style={{
            flex: 1,
            overflow: 'auto',
            padding: '1.5rem'
          }}>
            {error && (
              <div className="error-card" style={{ marginBottom: '1rem' }}>
                <h3 className="error-title">Fel</h3>
                <p className="error-message">{error}</p>
              </div>
            )}

            <div className="form-group">
              <label className="form-label">
                Tokennamn *
              </label>
              <input
                type="text"
                className="form-input"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                placeholder="Ange tokennamn"
                required
                maxLength={100}
              />
              <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                Unikt namn för denna token (max 100 tecken)
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">
                Beskrivning
              </label>
              <textarea
                className="form-input"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                placeholder="Ange beskrivning (valfritt)"
                maxLength={500}
                rows={3}
                style={{ resize: 'vertical', minHeight: '80px' }}
              />
              <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                Beskrivning av vad denna token används för (max 500 tecken)
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">
                Provider-typ *
              </label>
              <select
                className="form-input"
                value={formData.provider}
                onChange={(e) => handleChange('provider', e.target.value as OAuth2Provider)}
                disabled={isEditing}
              >
                <option value="manual">Manuell token</option>
                <option value="eEkonomi">eEkonomi (OAuth2)</option>
                <option value="Fortnox">Fortnox (OAuth2)</option>
              </select>
              <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                {isEditing ? 'Provider-typ kan inte ändras efter skapande' : 'Välj hur token ska hanteras'}
              </div>
            </div>

            {/* OAuth2 Integration Button */}
            {!isEditing && (formData.provider === 'eEkonomi' || formData.provider === 'Fortnox') && (
              <div style={{
                backgroundColor: '#f0f9ff',
                border: '1px solid #0ea5e9',
                borderRadius: '0.5rem',
                padding: '1rem',
                marginBottom: '1rem'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                  <span>🔗</span>
                  <span style={{ fontWeight: '500', color: '#0369a1' }}>OAuth2 Integration</span>
                </div>
                <p style={{ fontSize: '0.875rem', color: '#0369a1', margin: '0 0 1rem 0' }}>
                  Använd OAuth2 för säker auktorisering med {formData.provider}. Detta är det rekommenderade sättet.
                </p>
                <button
                  type="button"
                  onClick={() => setShowOAuth2Modal(true)}
                  className="action-button primary"
                  style={{ fontSize: '0.875rem' }}
                >
                  <span>🚀 Starta OAuth2-integration</span>
                </button>
              </div>
            )}

            {/* Manual Token Fields - only show for manual provider or when editing */}
            {(formData.provider === 'manual' || isEditing) && (
              <>

            <div className="form-group">
              <label className="form-label">
                API Token
              </label>
              <input
                type="password"
                className="form-input"
                value={formData.apiToken}
                onChange={(e) => handleChange('apiToken', e.target.value)}
                placeholder={isEditing ? "Lämna tomt för att behålla nuvarande" : "Ange API token (valfritt)"}
              />
              <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                {isEditing ? 'Lämna tomt för att behålla nuvarande token' : 'API token för integration'}
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">
                Refresh Token
              </label>
              <input
                type="password"
                className="form-input"
                value={formData.refreshToken}
                onChange={(e) => handleChange('refreshToken', e.target.value)}
                placeholder={isEditing ? "Lämna tomt för att behålla nuvarande" : "Ange refresh token (valfritt)"}
              />
              <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                {isEditing ? 'Lämna tomt för att behålla nuvarande token' : 'Refresh token för automatisk förnyelse'}
              </div>
            </div>
              </>
            )}

          {/* Actions */}
          <div style={{ 
            display: 'flex', 
            gap: '0.75rem', 
            justifyContent: 'flex-end', 
            padding: '1.5rem',
            borderTop: '1px solid #e5e7eb',
            flexShrink: 0
          }}>
            <button
              type="button"
              onClick={onCancel}
              className="action-button secondary"
            >
              <span>Avbryt</span>
            </button>
            <button
              type="submit"
              disabled={loading}
              className={`action-button ${loading ? 'secondary' : 'primary'}`}
              style={{ cursor: loading ? 'not-allowed' : 'pointer' }}
            >
              <span>{loading ? 'Sparar...' : (isEditing ? 'Uppdatera' : 'Skapa')}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
    </Portal>

    {/* OAuth2 Modal */}
    {showOAuth2Modal && (
      <OAuth2Modal
        customerId={customerId}
        onSuccess={handleOAuth2Success}
        onCancel={() => setShowOAuth2Modal(false)}
      />
    )}
    </>
  )
}
