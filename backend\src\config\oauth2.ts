import { OAuth2Config, OA<PERSON>2Provider } from '@rpa-project/shared';

// OAuth2 configuration for different providers
export const oauth2Configs: Record<OAuth2Provider, OAuth2Config | null> = {
  eEkonomi: {
    clientId: process.env.EEKONOMI_CLIENT_ID || '',
    clientSecret: process.env.EEKONOMI_CLIENT_SECRET || '',
    authUrl: 'https://identity.vismaonline.com/connect/authorize',
    tokenUrl: 'https://identity.vismaonline.com/connect/token',
    scopes: ['ea:api', 'offline_access', 'ea:sales', 'ea:accounting', 'ea:purchase'],
    redirectUri: process.env.EEKONOMI_REDIRECT_URI || 'http://localhost:3001/api/oauth2/callback/eEkonomi'
  },
  Fortnox: {
    clientId: process.env.FORTNOX_CLIENT_ID || '',
    clientSecret: process.env.FORTNOX_CLIENT_SECRET || '',
    authUrl: 'https://apps.fortnox.se/oauth-v1/auth',
    tokenUrl: 'https://apps.fortnox.se/oauth-v1/token',
    scopes: ['read', 'write'],
    redirectUri: process.env.FORTNOX_REDIRECT_URI || 'http://localhost:3001/api/oauth2/callback/Fortnox'
  },
  manual: null // Manual tokens don't use OAuth2
};

/**
 * Get OAuth2 configuration for a provider
 */
export function getOAuth2Config(provider: OAuth2Provider): OAuth2Config | null {
  return oauth2Configs[provider];
}

/**
 * Validate that OAuth2 configuration is complete for a provider
 */
export function validateOAuth2Config(provider: OAuth2Provider): boolean {
  const config = getOAuth2Config(provider);
  if (!config) return false;
  
  return !!(
    config.clientId &&
    config.clientSecret &&
    config.authUrl &&
    config.tokenUrl &&
    config.redirectUri
  );
}

/**
 * Generate OAuth2 authorization URL
 */
export function generateAuthUrl(provider: OAuth2Provider, state: string): string {
  const config = getOAuth2Config(provider);
  if (!config) {
    throw new Error(`OAuth2 configuration not found for provider: ${provider}`);
  }

  const params = new URLSearchParams({
    client_id: config.clientId,
    redirect_uri: config.redirectUri,
    scope: config.scopes.join(' '),
    response_type: 'code',
    state: state
  });

  // Add provider-specific parameters
  if (provider === 'eEkonomi') {
    params.append('prompt', 'select_account');
    params.append('acr_values', 'service:44643EB1-3F76-4C1C-A672-402AE8085934');
  }

  return `${config.authUrl}?${params.toString()}`;
}

/**
 * Generate state parameter for OAuth2 flow
 */
export function generateState(customerId: string, tokenName: string): string {
  const data = {
    customerId,
    tokenName,
    timestamp: Date.now()
  };
  return Buffer.from(JSON.stringify(data)).toString('base64url');
}

/**
 * Parse state parameter from OAuth2 callback
 */
export function parseState(state: string): { customerId: string; tokenName: string; timestamp: number } {
  try {
    const data = JSON.parse(Buffer.from(state, 'base64url').toString());
    return data;
  } catch (error) {
    throw new Error('Invalid state parameter');
  }
}
